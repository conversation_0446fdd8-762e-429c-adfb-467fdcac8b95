import { randomBytes, randomInt } from 'node:crypto';

export const generateRandomToken = (length = 32): Promise<string> =>
  new Promise((resolve, reject) => {
    randomBytes(length, (err, buffer) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(buffer.toString('hex'));
    });
  });

export const generateRandomOTP = (): Promise<number> =>
  new Promise((resolve, reject) => {
    randomInt(10000000, 100000000, (err, value) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(value);
    });
  });
