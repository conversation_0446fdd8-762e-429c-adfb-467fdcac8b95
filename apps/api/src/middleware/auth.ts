import { tryCatching } from '@/core/errors/try-catching';
import { sessions } from '@/database/schema';
import type { Env } from '@/types';
import { eq } from 'drizzle-orm';
import type { Context } from 'hono';
import { bearerAuth } from 'hono/bearer-auth';
import { createMiddleware } from 'hono/factory';

export const requireAuth = createMiddleware(async (c: Context<Env>, next) => {
  const bearer = bearerAuth({
    async verifyToken(bearer): Promise<boolean> {
      return tryCatching(async () => {
        const { token }: { token: string } = JSON.parse(atob(bearer));
        if (!token) return false;

        const db = c.get('db');
        const session = await db.query.sessions.findFirst({
          with: {
            user: true,
          },
          where: eq(sessions.token, token),
        });

        if (
          !session ||
          !session.user ||
          session.expiresAt < new Date() ||
          session.isRevoked
        ) {
          if (session) {
            await db.delete(sessions).where(eq(sessions.token, token));
          }
          return false;
        }

        c.set('session', session);
        c.set('user', session.user);

        return true;
      });
    },
  });

  return bearer(c, next);
});
