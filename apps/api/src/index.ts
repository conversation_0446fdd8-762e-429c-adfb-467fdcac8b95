import { HttpError } from '@/core/errors/custom-errors';
import { errorHandler } from '@/core/errors/error-handler';
import { buildJsonResponse } from '@/core/response';
import { createDb } from '@/database/db';
import { routes } from '@/routes';
import type { Env } from '@/types';
import { Hono } from 'hono';
import { contextStorage } from 'hono/context-storage';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { requestId } from 'hono/request-id';
import { timing } from 'hono/timing';

const app = new Hono<Env>();

app.use(
  '*',
  cors({
    origin: [],
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE'],
    credentials: true,
    maxAge: 86400,
  }),
);
app.use('*', timing());
app.use('*', logger());
app.use('*', requestId());
app.use('*', prettyJSON());
app.use('*', contextStorage());
app.use('*', (c, next) => {
  const env = c.env.NODE_ENV;
  const url = c.env.DATABASE_URL;
  c.set('db', createDb(url, env));
  return next();
});

app.route('/v1', routes);
app.get('/health', async (c) => {
  return buildJsonResponse({
    c,
    status: 200,
    message: 'OK!',
  });
});

app.notFound((c) => {
  throw new HttpError({
    statusCode: 404,
    message: `404: Endpoint ${c.req.method} ${c.req.path} not found`,
  });
});
app.onError(errorHandler);

export default app;
