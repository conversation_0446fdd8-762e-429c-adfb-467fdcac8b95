import { generateRandomToken } from '@/core/crypto';
import { ErrorCode, HttpStatusCode } from '@/core/errors/constants';
import { HttpError } from '@/core/errors/custom-errors';
import { verifyPassword } from '@/core/password';
import type { DB } from '@/database';
import { sessions, users } from '@/database/schema';
import type { SignIn } from '@muraadso/types/auth';
import type { Session } from '@muraadso/types/sessions';
import type { User } from '@muraadso/types/users';
import { eq } from 'drizzle-orm';

const SESSION_TOKEN_BYTES = 32;
const SYSOP_SESSION_TTL_MS = 15 * 60 * 1000; // 15 minutes
const CUSTOMER_SESSION_TTL_MS = 365 * 24 * 60 * 60 * 1000; // ~1 year

const computeTtlMs = (isSysop: boolean) =>
  isSysop ? SYSOP_SESSION_TTL_MS : CUSTOMER_SESSION_TTL_MS;

export const signIn = async (db: DB, { email, phone, password }: SignIn) => {
  const normalizedEmail = email?.toLowerCase();

  const user = await db.query.users.findFirst({
    where: (u, { eq, or }) =>
      normalizedEmail && phone
        ? or(eq(u.email, normalizedEmail), eq(u.phone, phone))
        : normalizedEmail
          ? eq(u.email, normalizedEmail)
          : eq(u.phone, phone!),
  });

  if (!user || !user.password) {
    throw new HttpError({
      statusCode: 401,
      message: 'Invalid credentials',
      code: ErrorCode.INVALID_EMAIL_OR_PASSWORD,
    });
  }

  const ok = verifyPassword(password, user.password);
  if (!ok) {
    throw new HttpError({
      statusCode: 401,
      message: 'Invalid credentials',
      code: ErrorCode.INVALID_EMAIL_OR_PASSWORD,
    });
  }

  return user;
};

export const signOut = async (db: DB, session?: Session) => {
  if (session?.id)
    await db.delete(sessions).where(eq(sessions.id, session?.id));
};

export const issueSession = async ({
  db,
  user,
  oldSession,
  mode,
  ipAddress,
  userAgent,
}: {
  db: DB;
  user: User;
  oldSession?: Session;
  ipAddress?: string;
  userAgent?: string;
  mode: 'verify_email' | 'reset_password' | 'sign_in';
}) => {
  // skip email verification requirement
  // if user is already verifying their email
  if (mode !== 'verify_email') {
    if (!user.emailVerifiedAt) {
      throw new HttpError({
        message:
          'Your account is pending verification. Please check your email to complete the process',
        statusCode: HttpStatusCode.UNAUTHORIZED,
        code: ErrorCode.AUTHENTICATION_FAILED,
      });
    }
  }

  if (user.isBanned) {
    throw new HttpError({
      message: 'Account banned. Please contact support for assistance',
      statusCode: HttpStatusCode.UNAUTHORIZED,
      code: ErrorCode.AUTHENTICATION_FAILED,
    });
  }

  const token = await generateRandomToken(SESSION_TOKEN_BYTES);
  const ttlMs = computeTtlMs(user.isSysop);
  const expiresAt = new Date(Date.now() + ttlMs);
  const maxAge = Math.floor(ttlMs / 1000);

  await db.transaction(async (tx) => {
    await tx.insert(sessions).values({
      token,
      expiresAt,
      ipAddress,
      userAgent,
      userId: user.id,
    });

    if (oldSession?.id) {
      await tx.delete(sessions).where(eq(sessions.id, oldSession.id));
    }

    await tx
      .update(users)
      .set({ lastSignInAt: new Date() })
      .where(eq(users.id, user.id));
  });

  return {
    token,
    expiresAt,
    maxAge,
  };
};
