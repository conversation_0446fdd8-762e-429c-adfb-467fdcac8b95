import { HttpError } from '@/core/errors/custom-errors';
import { handleZodError } from '@/core/errors/error-handler';
import { tryCatching } from '@/core/errors/try-catching';
import { buildJsonResponse } from '@/core/response';
import { requireAuth } from '@/middleware/auth';
import { issueSession, signIn, signOut } from '@/routes/auth.service';
import type { Env } from '@/types';
import { zValidator } from '@hono/zod-validator';
import { signInSchema } from '@muraadso/types/auth';
import { Hono } from 'hono';
import { getConnInfo } from 'hono/cloudflare-workers';
import { deleteCookie, setCookie } from 'hono/cookie';

export const route = new Hono<Env>();

route.post(
  '/sign-in',
  zValidator('json', signInSchema, handleZodError),
  async (c) => {
    return tryCatching(async () => {
      const db = c.get('db');
      const { email, phone, password } = c.req.valid('json');
      const user = await signIn(db, { email, phone, password });
      const { token, maxAge, expiresAt } = await issueSession({
        db,
        user,
        ipAddress: getConnInfo(c)?.remote?.address,
        userAgent: c.req.header('User-Agent'),
        mode: 'sign_in',
      });

      const session = JSON.stringify({ token, expiresAt, user });

      setCookie(c, 'session', token, {
        httpOnly: true,
        sameSite: 'Lax',
        secure: true,
        path: '/',
        maxAge,
      });

      return buildJsonResponse({
        c,
        status: 200,
        message: 'Signed in successfully!',
        payload: {
          user,
          session,
        },
      });
    });
  },
);

route.post('/sign-out', requireAuth, async (c) => {
  return tryCatching(async () => {
    const db = c.get('db');
    const session = c.get('session');

    await signOut(db, session);
    deleteCookie(c, 'session');

    return buildJsonResponse({ c, status: 200, message: 'Logged out' });
  });
});

route.get('/me', requireAuth, async (c) => {
  const user = c.get('user');
  const session = c.get('session')!;
  return buildJsonResponse({
    c,
    status: 200,
    payload: {
      user,
      session,
    },
  });
});

route.post('/extend-session', requireAuth, async (c) => {
  return tryCatching(async () => {
    const db = c.get('db');
    const session = c.get('session');
    const user = c.get('user');

    if (!user) {
      throw new HttpError({
        statusCode: 401,
        message: 'Unauthorized',
        context: {
          message: 'User not authenticated',
        },
      });
    }

    // Generate a new session with extended expiration time
    const newSession = await issueSession({
      db,
      user,
      oldSession: session,
      ipAddress: getConnInfo(c)?.remote?.address,
      userAgent: c.req.header('User-Agent'),
      mode: 'sign_in',
    });

    return buildJsonResponse({
      c,
      payload: { session: newSession },
      message: 'Session extended successfully',
    });
  });
});
