import { relations, sql } from 'drizzle-orm';
import { foreignKey, pgTable, primaryKey, unique } from 'drizzle-orm/pg-core';
import { users } from './users';

export const sessions = pgTable(
  'sessions',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: t.timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: t.timestamp({ withTimezone: true }).notNull().defaultNow(),
    userId: t.uuid().notNull(),
    token: t.text().notNull(),
    isRevoked: t.boolean().notNull().default(sql`FALSE`),
    expiresAt: t.timestamp({ withTimezone: true }).notNull(),
    ipAddress: t.inet(),
    userAgent: t.text(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.userId],
      foreignColumns: [users.id],
    }).onDelete('cascade'),
    unique().on(t.token),
  ],
);

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));
