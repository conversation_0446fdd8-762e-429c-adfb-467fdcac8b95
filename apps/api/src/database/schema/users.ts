import { sql } from 'drizzle-orm';
import { pgTable, primaryKey, unique } from 'drizzle-orm/pg-core';

export const users = pgTable(
  'users',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: t.timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: t.timestamp({ withTimezone: true }).notNull().defaultNow(),
    email: t.text(),
    emailVerifiedAt: t.timestamp({ withTimezone: true }),
    password: t.text(),
    phone: t.text(),
    phoneVerifiedAt: t.timestamp({ withTimezone: true }),
    verificationToken: t.text(),
    verificationSentAt: t.timestamp({ withTimezone: true }),
    recoveryToken: t.text(),
    recoverySentAt: t.timestamp({ withTimezone: true }),
    invitedAt: t.timestamp({ withTimezone: true }),
    lastSignInAt: t.timestamp({ withTimezone: true }),
    isBanned: t.boolean().notNull().default(sql`FALSE`),
    isSysop: t.boolean().notNull().default(sql`FALSE`),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    unique().on(t.email),
    unique().on(t.phone),
  ],
);
