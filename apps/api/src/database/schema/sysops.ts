import { relations } from 'drizzle-orm';
import { foreignKey, pgTable, primaryKey } from 'drizzle-orm/pg-core';
import { users } from './users';

export const sysops = pgTable(
  'sysops',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: t.timestamp({ withTimezone: true }).notNull().defaultNow(),
    updatedAt: t.timestamp({ withTimezone: true }).notNull().defaultNow(),
    userId: t.uuid().notNull(),
    name: t.text().notNull(),
    image: t.text(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.userId],
      foreignColumns: [users.id],
    }),
  ],
);

export const sysopsRelations = relations(sysops, ({ one }) => ({
  user: one(users, {
    fields: [sysops.userId],
    references: [users.id],
  }),
}));
