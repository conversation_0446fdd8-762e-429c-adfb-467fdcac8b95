import * as process from 'node:process';
import { sql } from 'drizzle-orm';
import {
  PgTable,
  type PgTransaction,
  getTableConfig,
} from 'drizzle-orm/pg-core';
import * as d from './data';
import { createDb } from './db';
import * as s from './schema';

// @ts-ignore
type Transaction = PgTransaction;

const seedUsers = (tx: Transaction) => {
  return tx.insert(s.users).values(d.generateUsersData()).returning({});
};

const seedSysops = (
  tx: Transaction,
  // users: ,
) => {
  return tx.insert(s.sysops).values(d.generateSysopsData(users)).returning({
    id: s.sysops.id,
    name: s.sysops.name,
    image: s.sysops.image,
    userId: s.sysops.userId,
  });
};

const seedCustomers = (
  tx: Transaction,
  // users:
) => {
  return tx
    .insert(s.customers)
    .values(d.generateCustomersData(users))
    .returning({
      id: s.customers.id,
      name: s.customers.name,
      phone: s.customers.phone,
      email: s.customers.email,
      image: s.customers.image,
      userId: s.customers.userId,
    });
};

(async () => {
  console.log('Starting database seeding...');
  const db = createDb(process.env.DATABASE_URL!);

  // Clean db first
  console.log('Truncating existing tables...');
  for (const table of Object.values(s)) {
    if (table instanceof PgTable) {
      const config = getTableConfig(table);
      config.schema = config.schema === undefined ? 'public' : config.schema;
      const tablesToTruncate = [`"${config.schema}"."${config.name}"`];
      await db.execute(
        sql.raw(`truncate ${tablesToTruncate.join(',')} cascade;`),
      );
    }
  }
  console.log('All tables truncated successfully.');

  await db.transaction(async (tx) => {
    const users = await seedUsers(tx);
    const [customers, sysops] = await Promise.all([
      // seedCustomers(tx, users),
      // seedSysops(tx, users),
    ]);
  });

  console.log('Seeding completed successfully ✅');
  process.exit(0);
})().catch((err) => {
  console.error(err);
  process.exit(1);
});
