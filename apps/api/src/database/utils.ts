import { type SQL, sql } from 'drizzle-orm';
import { type PgColumn, timestamp } from 'drizzle-orm/pg-core';
import { v4 as uuidv4 } from 'uuid';

export const lower = (col: PgColumn): SQL => {
  return sql`lower(${col})`;
};

const base62Chars =
  '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

const base62Encode = (buffer: Buffer): string => {
  let num = BigInt('0x' + buffer.toString('hex'));
  let result = '';
  while (num > 0n) {
    result = base62Chars[Number(num % 62n)] + result;
    num /= 62n;
  }
  return result || '0';
};

export const generateTrackingId = (prefix = 'ORD'): string => {
  const uuidBytes = Buffer.from(uuidv4().replace(/-/g, ''), 'hex');
  const encoded = base62Encode(uuidBytes);
  const shortId = encoded.slice(0, 5).toUpperCase(); // Get first 5 chars
  return `${prefix}-${shortId}`;
};
