import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { DefaultLogger } from './logger';
import * as schema from './schema';

export const createDb = (url: string, env?: string) => {
  const client = postgres(url);
  return drizzle(client, {
    schema: schema,
    logger: new DefaultLogger(env),
    casing: 'snake_case',
  });
};

export type DB = ReturnType<typeof createDb>;
