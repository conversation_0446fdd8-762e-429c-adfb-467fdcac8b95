{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 80, "ignore": ["node_modules", ".next", "dist"]}, "javascript": {"formatter": {"enabled": true, "quoteStyle": "single"}}, "linter": {"enabled": true, "rules": {"a11y": {"noSvgWithoutTitle": "off", "useKeyWithClickEvents": "off"}, "correctness": {"noUnusedVariables": "warn", "noChildrenProp": "off"}, "nursery": {"useSortedClasses": {"fix": "safe", "level": "info", "options": {"attributes": ["className", "class"], "functions": ["cn", "clsx", "cva"]}}}, "style": {"useTemplate": "off", "noNonNullAssertion": "off"}, "suspicious": {"noConfusingLabels": "off", "noArrayIndexKey": "off"}, "recommended": true}, "ignore": ["node_modules", ".next", "dist"]}, "organizeImports": {"enabled": true, "ignore": ["node_modules", ".next", "dist"]}}