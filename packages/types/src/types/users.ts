import { z } from 'zod/v4';

const userSchema = z.object({
  id: z.uuid(),
  email: z.email().nullable().optional(),
  emailVerifiedAt: z.date().nullable().optional(),
  phone: z.string().nullable().optional(),
  phoneVerifiedAt: z.date().nullable().optional(),
  password: z.string().nullable().optional(),
  verificationToken: z.string().nullable().optional(),
  verificationSentAt: z.date().nullable().optional(),
  recoveryToken: z.string().nullable().optional(),
  recoverySentAt: z.date().nullable().optional(),
  invitedAt: z.date().nullable().optional(),
  lastSignInAt: z.date().nullable().optional(),
  isBanned: z.boolean(),
  isSysop: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
});
export const selectUserSchema = userSchema;
export const createUserSchema = userSchema.partial({
  id: true,
  createdAt: true,
  updatedAt: true,
});
export const updateUserSchema = userSchema.partial();

export type User = z.infer<typeof selectUserSchema>;
export type CreateUser = z.infer<typeof createUserSchema>;
export type UpdateUser = z.infer<typeof updateUserSchema>;
