import { z } from 'zod/v4';

const sessionSchema = z.object({
  id: z.uuid(),
  token: z.string(),
  isRevoked: z.boolean(),
  expiresAt: z.date(),
  ipAddress: z.string().nullable().optional(),
  userAgent: z.string().nullable().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});
export const selectSessionSchema = sessionSchema;
export const createSessionSchema = sessionSchema.partial({
  id: true,
  createdAt: true,
  updatedAt: true,
});
export const updateSessionSchema = sessionSchema.partial();

export type Session = z.infer<typeof selectSessionSchema>;
export type CreateSession = z.infer<typeof createSessionSchema>;
export type UpdateSession = z.infer<typeof updateSessionSchema>;
