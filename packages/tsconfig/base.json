{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "compilerOptions": {"target": "ES2022", "module": "ESNext", "lib": ["ES2022", "DOM", "DOM.Iterable"], "incremental": true, "skipLibCheck": true, "strict": true, "allowJs": true, "declaration": true, "declarationMap": true, "esModuleInterop": true, "isolatedModules": true, "moduleDetection": "force", "moduleResolution": "Node", "forceConsistentCasingInFileNames": true, "noUncheckedIndexedAccess": true, "resolveJsonModule": true}}